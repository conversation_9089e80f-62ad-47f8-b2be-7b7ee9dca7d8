{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(PORT=3000 npm run dev)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npx next dev:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(npm run build:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx tsc:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx prisma generate:*)", "Bash(npx prisma migrate dev:*)", "Bash(DATABASE_URL=\"file:./dev.db\" npx prisma migrate dev --name add-tenant-signup)", "Bash(DATABASE_URL=\"file:./dev.db\" npx prisma migrate reset --force)", "Bash(DATABASE_URL=\"file:./dev.db\" npx prisma migrate dev --name init)", "<PERSON><PERSON>(cat:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python3 -m pip:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(python3 -m pytest tests/backend/ -v --tb=short)", "Bash(python3 -m pytest tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_home_page_loads -v)", "Bash(python3 -m pytest tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_home_page_loads -v -s --tb=short)", "Bash(python3 -m pytest tests/backend/test_live_api.py -v)", "Bash(pytest:*)", "Bash(python -m pytest tests/backend/ --html=test-reports/backend-report.html --self-contained-html -v)", "Bash(python3 -m pytest tests/backend/ --html=test-reports/backend-report.html --self-contained-html -v)"], "deny": []}}