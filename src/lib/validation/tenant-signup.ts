/**
 * Validation schemas for tenant signup workflow
 * Provides comprehensive input validation using Zod
 */

import { z } from 'zod';

// Reserved slugs that cannot be used by tenants
const RESERVED_SLUGS = new Set([
  'api', 'admin', 'app', 'www', 'mail', 'ftp', 'localhost',
  'dashboard', 'billing', 'support', 'help', 'docs', 'blog',
  'status', 'staging', 'dev', 'test', 'demo', 'cdn', 'assets',
  'static', 'public', 'private', 'secure', 'internal'
]);

// Tenant signup validation schema
export const tenantSignupSchema = z.object({
  // Tenant information
  tenant_name: z
    .string()
    .min(2, 'Business name must be at least 2 characters')
    .max(100, 'Business name cannot exceed 100 characters')
    .regex(/^[a-zA-Z0-9\s\-'&.]+$/, 'Business name contains invalid characters')
    .transform(val => val.trim()),

  tenant_slug: z
    .string()
    .min(3, 'URL must be at least 3 characters')
    .max(50, 'URL cannot exceed 50 characters')
    .regex(/^[a-z0-9-]+$/, 'URL can only contain lowercase letters, numbers, and hyphens')
    .refine(val => !val.startsWith('-') && !val.endsWith('-'), 'URL cannot start or end with hyphen')
    .refine(val => !val.includes('--'), 'URL cannot contain consecutive hyphens')
    .refine(val => !RESERVED_SLUGS.has(val), 'This URL is reserved and cannot be used')
    .transform(val => val.toLowerCase().trim()),

  // Admin user information
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email cannot exceed 255 characters')
    .transform(val => val.toLowerCase().trim()),

  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password cannot exceed 128 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character'),

  first_name: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name cannot exceed 50 characters')
    .transform(val => val.trim()),

  last_name: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name cannot exceed 50 characters')
    .transform(val => val.trim()),

  // Optional business details
  phone: z
    .string()
    .optional()
    .transform(val => val?.trim()),

  address: z
    .string()
    .optional()
    .transform(val => val?.trim()),

  city: z
    .string()
    .optional()
    .transform(val => val?.trim()),

  state: z
    .string()
    .optional()
    .transform(val => val?.trim()),

  zip_code: z
    .string()
    .optional()
    .transform(val => val?.trim()),
});

export type TenantSignupData = z.infer<typeof tenantSignupSchema>;

// Response schemas
export const tenantSignupResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  access_token: z.string(),
  token_type: z.string().default('bearer'),
  tenant: z.object({
    id: z.string(),
    name: z.string(),
    slug: z.string(),
    email: z.string(),
    status: z.string(),
    plan_type: z.string(),
    created_at: z.string(),
  }),
  user: z.object({
    id: z.string(),
    email: z.string(),
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    role: z.string(),
    status: z.string(),
    tenant_id: z.string(),
    created_at: z.string(),
  }),
  redirect_url: z.string(),
});

export const tenantSignupErrorSchema = z.object({
  success: z.boolean().default(false),
  error: z.string(),
  error_code: z.string(),
  details: z.record(z.string(), z.any()).optional(),
  field_errors: z.record(z.string(), z.array(z.string())).optional(),
});

// Availability check schema
export const availabilityCheckSchema = z.object({
  slug: z.string().optional(),
  email: z.string().email().optional(),
}).refine(
  data => data.slug || data.email,
  'Either slug or email must be provided'
);

export type AvailabilityCheckData = z.infer<typeof availabilityCheckSchema>;