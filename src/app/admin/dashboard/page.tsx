'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { 
  QrCode, 
  ShoppingCart, 
  Users, 
  BarChart, 
  Settings,
  Bell,
  Calendar,
  TrendingUp,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

export default function AdminDashboard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showWelcome, setShowWelcome] = useState(false);

  useEffect(() => {
    if (searchParams?.get('welcome') === 'true') {
      setShowWelcome(true);
    }
  }, [searchParams]);

  const stats = [
    { name: 'Active QR Codes', value: '24', icon: QrCode, color: 'text-blue-600 bg-blue-100' },
    { name: 'Orders Today', value: '156', icon: ShoppingCart, color: 'text-green-600 bg-green-100' },
    { name: 'Active Staff', value: '8', icon: Users, color: 'text-purple-600 bg-purple-100' },
    { name: 'Revenue Today', value: '$2,340', icon: TrendingUp, color: 'text-yellow-600 bg-yellow-100' },
  ];

  const quickActions = [
    {
      name: 'QR Code Management',
      description: 'Generate and manage QR codes for tables',
      icon: QrCode,
      action: () => router.push('/admin/qr'),
      color: 'bg-orange-600 hover:bg-orange-700'
    },
    {
      name: 'Order Management',
      description: 'View and manage incoming orders',
      icon: ShoppingCart,
      action: () => router.push('/admin/orders'),
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      name: 'Staff Management',
      description: 'Manage staff and permissions',
      icon: Users,
      action: () => router.push('/admin/staff'),
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      name: 'Analytics',
      description: 'View performance metrics',
      icon: BarChart,
      action: () => router.push('/admin/analytics'),
      color: 'bg-purple-600 hover:bg-purple-700'
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back! Here's what's happening today.</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="w-5 h-5" />
              </button>
              <button
                onClick={() => router.push('/')}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Welcome Banner */}
      {showWelcome && (
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <div className="px-6 py-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <CheckCircle className="w-8 h-8" />
                <div>
                  <h2 className="text-2xl font-bold">Welcome to BheemDine! 🎉</h2>
                  <p className="text-orange-100">Your account has been created successfully. Let's get your restaurant set up!</p>
                </div>
              </div>
              <button
                onClick={() => setShowWelcome(false)}
                className="text-orange-100 hover:text-white transition-colors text-xl"
              >
                ✕
              </button>
            </div>
            
            {/* Quick Setup Steps */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white bg-opacity-10 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-300" />
                  <span className="font-medium">Account Created</span>
                </div>
              </div>
              <button
                onClick={() => router.push('/admin/menu')}
                className="bg-white bg-opacity-10 rounded-lg p-4 hover:bg-opacity-20 transition-colors text-left group"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 border-2 border-orange-200 rounded-full"></div>
                    <span className="font-medium">Add Menu Items</span>
                  </div>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </div>
              </button>
              <button
                onClick={() => router.push('/admin/qr')}
                className="bg-white bg-opacity-10 rounded-lg p-4 hover:bg-opacity-20 transition-colors text-left group"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 border-2 border-orange-200 rounded-full"></div>
                    <span className="font-medium">Generate QR Codes</span>
                  </div>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </div>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="px-6 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <stat.icon className="w-6 h-6" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <button
                key={action.name}
                onClick={action.action}
                className={`p-6 rounded-lg text-white text-left transition-colors ${action.color}`}
              >
                <action.icon className="w-8 h-8 mb-3" />
                <h3 className="font-semibold mb-1">{action.name}</h3>
                <p className="text-sm opacity-90">{action.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Orders */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Orders</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {[
                  { id: '#1234', table: 'Table 5', amount: '$45.20', status: 'Preparing', time: '5 min ago' },
                  { id: '#1235', table: 'Room 201', amount: '$82.50', status: 'Ready', time: '12 min ago' },
                  { id: '#1236', table: 'Table 3', amount: '$28.75', status: 'Delivered', time: '18 min ago' },
                ].map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{order.id} - {order.table}</div>
                      <div className="text-sm text-gray-600">{order.time}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-gray-900">{order.amount}</div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        order.status === 'Preparing' ? 'bg-yellow-100 text-yellow-800' :
                        order.status === 'Ready' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => router.push('/admin/orders')}
                className="w-full mt-4 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                View All Orders
              </button>
            </div>
          </div>

          {/* QR Code Status */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">QR Code Status</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {[
                  { room: 'Table 1-10', status: 'Active', scans: 45, color: 'text-green-600' },
                  { room: 'Room 201-205', status: 'Active', scans: 23, color: 'text-green-600' },
                  { room: 'Table 11-15', status: 'Needs Update', scans: 12, color: 'text-yellow-600' },
                ].map((qr, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{qr.room}</div>
                      <div className={`text-sm ${qr.color}`}>{qr.status}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-gray-900">{qr.scans} scans</div>
                      <div className="text-sm text-gray-600">today</div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => router.push('/admin/qr')}
                className="w-full mt-4 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Manage QR Codes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}