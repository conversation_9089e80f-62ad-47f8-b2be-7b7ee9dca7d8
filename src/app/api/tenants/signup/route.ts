/**
 * Tenant signup API endpoint
 * POST /api/tenants/signup
 */

import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { tenantSignupSchema } from '@/lib/validation/tenant-signup';
import { TenantService } from '@/lib/api/tenant-service';

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const signupData = tenantSignupSchema.parse(body);

    // Execute tenant signup workflow
    const result = await TenantService.createTenantWithAdmin(signupData);

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Tenant created successfully',
      access_token: result.access_token,
      token_type: 'bearer',
      tenant: result.tenant,
      user: result.user,
      redirect_url: '/dashboard'
    }, { status: 201 });

  } catch (error) {
    console.error('Tenant signup error:', error);

    // Handle Zod validation errors
    if (error instanceof ZodError) {
      const fieldErrors: Record<string, string[]> = {};
      
      if (error.issues && Array.isArray(error.issues)) {
        error.issues.forEach((err) => {
          const field = err.path[0] as string;
          if (!fieldErrors[field]) {
            fieldErrors[field] = [];
          }
          fieldErrors[field].push(err.message);
        });
      }

      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        error_code: 'VALIDATION_ERROR',
        field_errors: fieldErrors
      }, { status: 400 });
    }

    // Handle known business logic errors
    if (error instanceof Error) {
      let errorCode = 'UNKNOWN_ERROR';
      let statusCode = 500;

      if (error.message.includes('already registered') || error.message.includes('already taken')) {
        errorCode = 'DUPLICATE_ERROR';
        statusCode = 400;
      } else if (error.message.includes('authentication') || error.message.includes('password')) {
        errorCode = 'AUTH_ERROR';
        statusCode = 401;
      } else if (error.message.includes('database') || error.message.includes('transaction')) {
        errorCode = 'DATABASE_ERROR';
        statusCode = 500;
      }

      return NextResponse.json({
        success: false,
        error: error.message,
        error_code: errorCode
      }, { status: statusCode });
    }

    // Handle unexpected errors
    return NextResponse.json({
      success: false,
      error: 'An unexpected error occurred',
      error_code: 'INTERNAL_ERROR'
    }, { status: 500 });
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed',
    error_code: 'METHOD_NOT_ALLOWED'
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed',
    error_code: 'METHOD_NOT_ALLOWED'
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    success: false,
    error: 'Method not allowed',
    error_code: 'METHOD_NOT_ALLOWED'
  }, { status: 405 });
}