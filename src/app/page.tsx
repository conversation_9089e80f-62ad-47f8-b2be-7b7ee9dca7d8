'use client';

import { useRouter } from 'next/navigation';
import { QrCode, Users, ShoppingCart, BarChart } from 'lucide-react';

export default function HomePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-b from-orange-50 to-white">
      {/* Header */}
      <header className="px-6 py-4 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">B</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">BHEEMDINE</h1>
          </div>
          <nav className="flex items-center space-x-6">
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              Admin Dashboard
            </button>
            <button
              onClick={() => router.push('/menu')}
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              View Demo Menu
            </button>
            <button
              onClick={() => router.push('/signup')}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              Start Free Trial
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="px-6 py-20">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Digital Restaurant Management
            <span className="block text-orange-600 mt-2">Made Simple</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Transform your restaurant with QR-based digital menus, real-time order management, 
            and comprehensive staff administration tools.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button
              onClick={() => router.push('/signup')}
              className="px-8 py-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Start Your Free Trial
            </button>
            <button
              onClick={() => router.push('/menu')}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              View Demo Menu
            </button>
          </div>
          <div className="mt-8 flex items-center justify-center space-x-6 text-sm text-gray-500">
            <span className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>30-day free trial</span>
            </span>
            <span className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>No credit card required</span>
            </span>
            <span className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Setup in 5 minutes</span>
            </span>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="px-6 py-16 bg-white">
        <div className="max-w-7xl mx-auto">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Everything You Need to Manage Your Restaurant
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FeatureCard
              icon={<QrCode className="w-8 h-8 text-orange-600" />}
              title="QR Code Management"
              description="Generate, customize, and manage QR codes for all your tables and rooms"
              onClick={() => router.push('/admin/qr')}
            />
            <FeatureCard
              icon={<ShoppingCart className="w-8 h-8 text-orange-600" />}
              title="Real-time Orders"
              description="Track and manage orders in real-time with instant notifications"
              onClick={() => router.push('/admin/orders')}
            />
            <FeatureCard
              icon={<Users className="w-8 h-8 text-orange-600" />}
              title="Staff Management"
              description="Manage staff roles, permissions, and access control"
              onClick={() => router.push('/admin/staff')}
            />
            <FeatureCard
              icon={<BarChart className="w-8 h-8 text-orange-600" />}
              title="Analytics Dashboard"
              description="Track performance metrics and gain business insights"
              onClick={() => router.push('/admin/analytics')}
            />
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section className="px-6 py-16">
        <div className="max-w-4xl mx-auto bg-orange-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">
            Try Our Demo
          </h3>
          <p className="text-gray-600 text-center mb-6">
            Experience the platform with our demo restaurant
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => router.push('/menu?qr=demo')}
              className="p-4 bg-white rounded-lg border border-orange-200 hover:border-orange-300 transition-colors"
            >
              <h4 className="font-semibold text-gray-900 mb-2">Customer View</h4>
              <p className="text-sm text-gray-600">
                Scan QR code and order from digital menu
              </p>
            </button>
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="p-4 bg-white rounded-lg border border-orange-200 hover:border-orange-300 transition-colors"
            >
              <h4 className="font-semibold text-gray-900 mb-2">Admin Dashboard</h4>
              <p className="text-sm text-gray-600">
                Manage orders, menu, and staff
              </p>
            </button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-8 bg-gray-50 border-t border-gray-200">
        <div className="max-w-7xl mx-auto text-center text-gray-600">
          <p>&copy; 2024 BHEEMDINE. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}

function FeatureCard({ 
  icon, 
  title, 
  description, 
  onClick 
}: { 
  icon: React.ReactNode; 
  title: string; 
  description: string;
  onClick: () => void;
}) {
  return (
    <button
      onClick={onClick}
      className="p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors text-left group"
    >
      <div className="mb-4 group-hover:scale-110 transition-transform">
        {icon}
      </div>
      <h4 className="text-lg font-semibold text-gray-900 mb-2">{title}</h4>
      <p className="text-gray-600 text-sm">{description}</p>
    </button>
  );
}