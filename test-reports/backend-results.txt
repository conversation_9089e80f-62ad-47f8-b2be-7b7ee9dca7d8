============================= test session starts ==============================
platform darwin -- Python 3.9.6, pytest-7.4.3, pluggy-1.6.0 -- /Library/Developer/CommandLineTools/usr/bin/python3
cachedir: .pytest_cache
rootdir: /Users/<USER>/Desktop/BHEEMDINE
configfile: pytest.ini
plugins: Faker-37.4.0, asyncio-0.21.1, cov-6.2.1, anyio-3.7.1
asyncio: mode=strict
collecting ... collected 22 items

tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_endpoint_exists FAILED [  4%]
tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_exists FAILED [  9%]
tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_validation FAILED [ 13%]
tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_unsupported_methods FAILED [ 18%]
tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_home_page_loads FAILED [ 22%]
tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_page_loads FAILED [ 27%]
tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_admin_dashboard_loads FAILED [ 31%]
tests/backend/test_live_api.py::TestLiveAPI::test_home_page_loads PASSED [ 36%]
tests/backend/test_live_api.py::TestLiveAPI::test_signup_page_loads PASSED [ 40%]
tests/backend/test_live_api.py::TestLiveAPI::test_admin_dashboard_loads PASSED [ 45%]
tests/backend/test_live_api.py::TestLiveAPI::test_availability_check_endpoint PASSED [ 50%]
tests/backend/test_live_api.py::TestLiveAPI::test_availability_endpoint_validation PASSED [ 54%]
tests/backend/test_live_api.py::TestLiveAPI::test_signup_endpoint_validation PASSED [ 59%]
tests/backend/test_live_api.py::TestLiveAPI::test_signup_password_validation PASSED [ 63%]
tests/backend/test_live_api.py::TestLiveAPI::test_signup_with_valid_data_structure PASSED [ 68%]
tests/backend/test_live_api.py::TestLiveAPI::test_unsupported_methods PASSED [ 72%]
tests/backend/test_tenant_signup.py::TestTenantSignup::test_valid_tenant_signup_validation FAILED [ 77%]
tests/backend/test_tenant_signup.py::TestTenantSignup::test_invalid_tenant_signup FAILED [ 81%]
tests/backend/test_tenant_signup.py::TestTenantSignup::test_availability_check_endpoint FAILED [ 86%]
tests/backend/test_tenant_signup.py::TestTenantSignup::test_password_validation FAILED [ 90%]
tests/backend/test_tenant_signup.py::TestTenantSignup::test_missing_required_fields FAILED [ 95%]
tests/backend/test_tenant_signup.py::TestTenantSignup::test_email_format_validation FAILED [100%]

=================================== FAILURES ===================================
_________________ TestAPIEndpoints.test_signup_endpoint_exists _________________
tests/backend/test_api_endpoints.py:11: in test_signup_endpoint_exists
    response = await client.post("/api/tenants/signup", json={})
E   AttributeError: 'async_generator' object has no attribute 'post'
______________ TestAPIEndpoints.test_availability_endpoint_exists ______________
tests/backend/test_api_endpoints.py:23: in test_availability_endpoint_exists
    response = await client.get("/api/tenants/check-availability?email=<EMAIL>")
E   AttributeError: 'async_generator' object has no attribute 'get'
____________ TestAPIEndpoints.test_availability_endpoint_validation ____________
tests/backend/test_api_endpoints.py:35: in test_availability_endpoint_validation
    response = await client.get("/api/tenants/check-availability")
E   AttributeError: 'async_generator' object has no attribute 'get'
__________________ TestAPIEndpoints.test_unsupported_methods ___________________
tests/backend/test_api_endpoints.py:46: in test_unsupported_methods
    response = await client.get("/api/tenants/signup")
E   AttributeError: 'async_generator' object has no attribute 'get'
____________________ TestAPIEndpoints.test_home_page_loads _____________________
tests/backend/test_api_endpoints.py:62: in test_home_page_loads
    response = await client.get("/")
E   AttributeError: 'async_generator' object has no attribute 'get'
___________________ TestAPIEndpoints.test_signup_page_loads ____________________
tests/backend/test_api_endpoints.py:70: in test_signup_page_loads
    response = await client.get("/signup")
E   AttributeError: 'async_generator' object has no attribute 'get'
_________________ TestAPIEndpoints.test_admin_dashboard_loads __________________
tests/backend/test_api_endpoints.py:78: in test_admin_dashboard_loads
    response = await client.get("/admin/dashboard")
E   AttributeError: 'async_generator' object has no attribute 'get'
_____________ TestTenantSignup.test_valid_tenant_signup_validation _____________
tests/backend/test_tenant_signup.py:15: in test_valid_tenant_signup_validation
    response = await client.post("/api/tenants/signup", json=valid_tenant_data)
E   AttributeError: 'async_generator' object has no attribute 'post'
_________________ TestTenantSignup.test_invalid_tenant_signup __________________
tests/backend/test_tenant_signup.py:47: in test_invalid_tenant_signup
    response = await client.post("/api/tenants/signup", json=invalid_tenant_data)
E   AttributeError: 'async_generator' object has no attribute 'post'
______________ TestTenantSignup.test_availability_check_endpoint _______________
tests/backend/test_tenant_signup.py:70: in test_availability_check_endpoint
    response = await client.get(
E   AttributeError: 'async_generator' object has no attribute 'get'
__________________ TestTenantSignup.test_password_validation ___________________
tests/backend/test_tenant_signup.py:105: in test_password_validation
    response = await client.post("/api/tenants/signup", json=data)
E   AttributeError: 'async_generator' object has no attribute 'post'
________________ TestTenantSignup.test_missing_required_fields _________________
tests/backend/test_tenant_signup.py:119: in test_missing_required_fields
    response = await client.post("/api/tenants/signup", json={})
E   AttributeError: 'async_generator' object has no attribute 'post'
________________ TestTenantSignup.test_email_format_validation _________________
tests/backend/test_tenant_signup.py:148: in test_email_format_validation
    response = await client.post("/api/tenants/signup", json=data)
E   AttributeError: 'async_generator' object has no attribute 'post'
=========================== short test summary info ============================
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_endpoint_exists
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_exists
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_validation
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_unsupported_methods
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_home_page_loads
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_page_loads
FAILED tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_admin_dashboard_loads
FAILED tests/backend/test_tenant_signup.py::TestTenantSignup::test_valid_tenant_signup_validation
FAILED tests/backend/test_tenant_signup.py::TestTenantSignup::test_invalid_tenant_signup
FAILED tests/backend/test_tenant_signup.py::TestTenantSignup::test_availability_check_endpoint
FAILED tests/backend/test_tenant_signup.py::TestTenantSignup::test_password_validation
FAILED tests/backend/test_tenant_signup.py::TestTenantSignup::test_missing_required_fields
FAILED tests/backend/test_tenant_signup.py::TestTenantSignup::test_email_format_validation
========================= 13 failed, 9 passed in 0.25s =========================
