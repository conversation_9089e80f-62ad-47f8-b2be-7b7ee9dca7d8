
> bhee<PERSON><PERSON>@1.0.0 dev
> next dev

   ▲ Next.js 14.0.4
   - Local:        http://localhost:3000
   - Environments: .env.local

 ✓ Ready in 1387ms
 ○ Compiling /api/tenants/signup ...
 ✓ Compiled /api/tenants/signup in 534ms (202 modules)
Tenant signup error: ZodError: [
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[A-Z]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one uppercase letter"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[!@#$%^&*(),.?\":{}|<>]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one special character"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ⨯ TypeError: Cannot read properties of undefined (reading 'forEach')
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:43:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
Tenant signup error: SyntaxError: Unexpected token ! in JSON at position 154
    at JSON.parse (<anonymous>)
    at parseJSONFromBytes (node:internal/deps/undici/undici:5595:19)
    at successSteps (node:internal/deps/undici/undici:5566:27)
    at fullyReadBody (node:internal/deps/undici/undici:1665:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async specConsumeBody (node:internal/deps/undici/undici:5575:7)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:22:22)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:info Starting a sqlite pool with 21 connections.
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ✓ Compiled /api/tenants/check-availability in 73ms (204 modules)
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
 ○ Compiling / ...
 ✓ Compiled / in 1719ms (636 modules)
 ✓ Compiled in 110ms (228 modules)
 ✓ Compiled /not-found in 157ms (618 modules)
 ✓ Compiled in 179ms (445 modules)
 ✓ Compiled in 158ms (445 modules)
 ✓ Compiled in 127ms (230 modules)
 ✓ Compiled in 61ms (431 modules)
 ✓ Compiled /signup in 211ms (457 modules)
 ✓ Compiled in 95ms (242 modules)
 ✓ Compiled in 82ms (242 modules)
 ✓ Compiled /admin/dashboard in 147ms (461 modules)
 ✓ Compiled /signup in 21ms (239 modules)
 ✓ Compiled /api/tenants/check-availability in 182ms (356 modules)
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
 ✓ Compiled /api/tenants/signup in 83ms (423 modules)
Tenant signup error: ZodError: [
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_slug"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "email"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "password"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "first_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "last_name"
    ],
    "message": "Invalid input"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ⨯ TypeError: Cannot read properties of undefined (reading 'forEach')
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:43:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
Tenant signup error: ZodError: [
  {
    "origin": "string",
    "code": "too_small",
    "minimum": 8,
    "inclusive": true,
    "path": [
      "password"
    ],
    "message": "Password must be at least 8 characters"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[A-Z]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one uppercase letter"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/\\d/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one number"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[!@#$%^&*(),.?\":{}|<>]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one special character"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ⨯ TypeError: Cannot read properties of undefined (reading 'forEach')
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:43:26)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ✓ Compiled in 168ms (671 modules)
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
Tenant signup error: ZodError: [
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_slug"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "email"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "password"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "first_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "last_name"
    ],
    "message": "Invalid input"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
Tenant signup error: ZodError: [
  {
    "origin": "string",
    "code": "too_small",
    "minimum": 8,
    "inclusive": true,
    "path": [
      "password"
    ],
    "message": "Password must be at least 8 characters"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[A-Z]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one uppercase letter"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/\\d/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one number"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[!@#$%^&*(),.?\":{}|<>]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one special character"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ✓ Compiled /not-found in 73ms (662 modules)
 ○ Compiling /admin/qr ...
 ✓ Compiled /admin/qr in 1121ms (1153 modules)
 ✓ Compiled /menu in 451ms (1163 modules)
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
Tenant signup error: ZodError: [
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_slug"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "email"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "password"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "first_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "last_name"
    ],
    "message": "Invalid input"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
Tenant signup error: ZodError: [
  {
    "origin": "string",
    "code": "too_small",
    "minimum": 8,
    "inclusive": true,
    "path": [
      "password"
    ],
    "message": "Password must be at least 8 characters"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[A-Z]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one uppercase letter"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/\\d/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one number"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[!@#$%^&*(),.?\":{}|<>]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one special character"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
Tenant signup error: ZodError: [
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_slug"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "email"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "password"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "first_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "last_name"
    ],
    "message": "Invalid input"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
Tenant signup error: ZodError: [
  {
    "origin": "string",
    "code": "too_small",
    "minimum": 8,
    "inclusive": true,
    "path": [
      "password"
    ],
    "message": "Password must be at least 8 characters"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[A-Z]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one uppercase letter"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/\\d/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one number"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[!@#$%^&*(),.?\":{}|<>]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one special character"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT 1
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
Tenant signup error: ZodError: [
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "tenant_slug"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "email"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "password"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "first_name"
    ],
    "message": "Invalid input"
  },
  {
    "expected": "string",
    "code": "invalid_type",
    "path": [
      "last_name"
    ],
    "message": "Invalid input"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
Tenant signup error: ZodError: [
  {
    "origin": "string",
    "code": "too_small",
    "minimum": 8,
    "inclusive": true,
    "path": [
      "password"
    ],
    "message": "Password must be at least 8 characters"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[A-Z]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one uppercase letter"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/\\d/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one number"
  },
  {
    "origin": "string",
    "code": "invalid_format",
    "format": "regex",
    "pattern": "/[!@#$%^&*(),.?\":{}|<>]/",
    "path": [
      "password"
    ],
    "message": "Password must contain at least one special character"
  }
]
    at POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:23:106)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
prisma:query SELECT `main`.`AdminUser`.`id`, `main`.`AdminUser`.`tenantId`, `main`.`AdminUser`.`authUserId`, `main`.`AdminUser`.`email`, `main`.`AdminUser`.`firstName`, `main`.`AdminUser`.`lastName`, `main`.`AdminUser`.`phone`, `main`.`AdminUser`.`avatar`, `main`.`AdminUser`.`role`, `main`.`AdminUser`.`status`, `main`.`AdminUser`.`permissions`, `main`.`AdminUser`.`preferences`, `main`.`AdminUser`.`timezone`, `main`.`AdminUser`.`language`, `main`.`AdminUser`.`lastLoginAt`, `main`.`AdminUser`.`isOnline`, `main`.`AdminUser`.`createdAt`, `main`.`AdminUser`.`updatedAt` FROM `main`.`AdminUser` WHERE (`main`.`AdminUser`.`email` = ? AND 1=1) LIMIT ? OFFSET ?
prisma:query SELECT `main`.`Tenant`.`id`, `main`.`Tenant`.`name`, `main`.`Tenant`.`slug`, `main`.`Tenant`.`email`, `main`.`Tenant`.`phone`, `main`.`Tenant`.`address`, `main`.`Tenant`.`city`, `main`.`Tenant`.`state`, `main`.`Tenant`.`country`, `main`.`Tenant`.`zipCode`, `main`.`Tenant`.`logoUrl`, `main`.`Tenant`.`primaryColor`, `main`.`Tenant`.`description`, `main`.`Tenant`.`website`, `main`.`Tenant`.`status`, `main`.`Tenant`.`planType`, `main`.`Tenant`.`planExpiry`, `main`.`Tenant`.`settings`, `main`.`Tenant`.`features`, `main`.`Tenant`.`isActive`, `main`.`Tenant`.`isDeleted`, `main`.`Tenant`.`deletedAt`, `main`.`Tenant`.`createdAt`, `main`.`Tenant`.`updatedAt` FROM `main`.`Tenant` WHERE (`main`.`Tenant`.`slug` = ? AND 1=1) LIMIT ? OFFSET ?
TypeError: fetch failed
    at node:internal/deps/undici/undici:12637:11
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:110:18)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  cause: Error: getaddrinfo ENOTFOUND placeholder.supabase.co
      at GetAddrInfoReqWrap.onlookup [as oncomplete] (node:dns:107:26)
      at GetAddrInfoReqWrap.callbackTrampoline (node:internal/async_hooks:128:17) {
    errno: -3008,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'placeholder.supabase.co'
  }
}
Supabase user creation error: AuthRetryableFetchError: fetch failed
    at _handleRequest (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:114:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async _request (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js:97:18)
    at async GoTrueAdminApi.createUser (webpack-internal:///(rsc)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js:128:20)
    at async createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:28:33)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251 {
  __isAuthError: true,
  status: 0,
  code: undefined
}
Tenant signup error: Error: Failed to create user account
    at createAuthUser (webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts:51:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async TenantService.createTenantWithAdmin (webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts:47:28)
    at async POST (webpack-internal:///(rsc)/./src/app/api/tenants/signup/route.ts:25:24)
    at async /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:6:63251
 ✓ Compiled in 212ms (973 modules)
 ✓ Compiled in 190ms (973 modules)
 ✓ Compiled in 83ms (973 modules)
 ✓ Compiled in 92ms (973 modules)
 ✓ Compiled in 229ms (483 modules)
 ✓ Compiled /_error in 426ms (1140 modules)
 ⨯ Error: Cannot find module './638.js'
Require stack:
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:55:36
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at __webpack_require__.f.require (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:198:28)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7018
    at Object.<anonymous> (/Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7063)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at requirePage (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:84
    at async loadComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/next-server.js:671:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js',
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './638.js'
Require stack:
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:55:36
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at __webpack_require__.f.require (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:198:28)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7018
    at Object.<anonymous> (/Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7063)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at requirePage (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:84
    at async loadComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/next-server.js:671:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js',
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './638.js'
Require stack:
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:55:36
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at __webpack_require__.f.require (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:198:28)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7018
    at Object.<anonymous> (/Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7063)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at requirePage (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:84
    at async loadComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/next-server.js:671:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js',
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './638.js'
Require stack:
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:55:36
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at __webpack_require__.f.require (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:198:28)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7018
    at Object.<anonymous> (/Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7063)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at requirePage (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:84
    at async loadComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/next-server.js:671:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js',
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './638.js'
Require stack:
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:55:36
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at __webpack_require__.f.require (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:198:28)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7018
    at Object.<anonymous> (/Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7063)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at requirePage (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:84
    at async loadComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/next-server.js:671:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js',
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
 ⨯ Error: Cannot find module './638.js'
Require stack:
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
- /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js
- /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:55:36
    at Module._load (node:internal/modules/cjs/loader:981:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at __webpack_require__.f.require (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:198:28)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:111:40
    at Array.reduce (<anonymous>)
    at __webpack_require__.e (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:110:67)
    at Array.map (<anonymous>)
    at __webpack_require__.X (/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js:162:22)
    at /Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7018
    at Object.<anonymous> (/Users/<USER>/Desktop/BHEEMDINE/.next/server/app/page.js:26:7063)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at mod.require (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:177:18)
    at requirePage (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js:109:84)
    at /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:84
    at async loadComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js:59:26)
    at async DevServer.findPageComponentsImpl (/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/next-server.js:671:36) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js',
    '/Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_document.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-middleware.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/dev/hot-reloader-webpack.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/router-server.js',
    '/Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/lib/start-server.js'
  ],
  page: '/'
}
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, lstat '/Users/<USER>/Desktop/BHEEMDINE/.next/server/vendor-chunks'
<w> [webpack.cache.PackFileCacheStrategy/webpack.FileSystemInfo] Resolving './vendor-chunks/@swc' in /Users/<USER>/Desktop/BHEEMDINE/.next/server for build dependencies doesn't lead to expected result '/Users/<USER>/Desktop/BHEEMDINE/.next/server/vendor-chunks/@swc.js', but to 'Error: Can't resolve './vendor-chunks/@swc' in '/Users/<USER>/Desktop/BHEEMDINE/.next/server'' instead. Resolving dependencies are ignored for this path.
<w>  at resolve commonjs file ./vendor-chunks/@swc (expected /Users/<USER>/Desktop/BHEEMDINE/.next/server/vendor-chunks/@swc.js)
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/.next/server/webpack-runtime.js
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_error.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/.next/server/pages/_error.js
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/require.js
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/server/load-components.js
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/utils.js
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/worker.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/worker.js
<w>  at file dependencies /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js
<w>  at file /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js
<w>  at resolve commonjs /Users/<USER>/Desktop/BHEEMDINE/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js
[?25h
