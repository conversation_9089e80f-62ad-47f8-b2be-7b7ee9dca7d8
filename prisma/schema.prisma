// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Multi-tenant root entity - represents restaurant/hotel chains or individual establishments
model Tenant {
  id          String   @id @default(uuid())
  name        String   // Restaurant/Hotel name
  slug        String   @unique // URL-friendly identifier (e.g., "marriott-downtown")
  email       String   @unique // Primary contact email
  phone       String?  // Business phone number
  address     String?  // Business address
  city        String?  // Business city
  state       String?  // Business state/province
  country     String   @default("US") // Business country
  zipCode     String?  // Business ZIP/postal code
  logoUrl     String?  // Brand logo
  primaryColor String? @default("#FF6B35") // Brand color for theming
  description String?  // Business description
  website     String?  // Business website
  
  // Tenant status and subscription
  status      TenantStatus @default(ACTIVE)
  planType    PlanType     @default(TRIAL)
  planExpiry  DateTime?    // When current plan expires
  
  // Feature flags and settings
  settings    String?    @default("{}") // Tenant-specific settings (theme, features, etc.)
  features    String     @default("{\"qr_ordering\": true, \"realtime_orders\": true, \"analytics\": false}") // Feature toggles
  
  isActive    Boolean  @default(true)
  isDeleted   Boolean  @default(false) // Soft delete flag
  deletedAt   DateTime? // Timestamp of soft delete
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  users       User[]
  adminUsers  AdminUser[] // Tenant administrators/staff
  rooms       Room[]
  menuItems   MenuItem[]
  orders      Order[]
  staff       Staff[]
  allergens   Allergen[]
  orderEvents OrderEvent[]
  auditLogs   AuditLog[]  // Security audit trail
  
  // Indexes for performance
  @@index([slug])
  @@index([isActive])
  @@index([isDeleted])
}

// Users who place orders (customers/guests)
model User {
  id          String   @id @default(uuid())
  tenantId    String   // Foreign key to Tenant
  email       String   @default("guest@local") // Default for guest users
  phone       String   @default("0000000000") // Default for guest users
  name        String
  roomId      String?  // Current room assignment (nullable for non-guests)
  isGuest     Boolean  @default(true) // Distinguish between guests and registered users
  lastActive  DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  room        Room?    @relation(fields: [roomId], references: [id], onDelete: SetNull)
  orders      Order[]
  
  // Indexes for performance
  @@unique([tenantId, email]) // Email unique per tenant
  @@unique([tenantId, phone]) // Phone unique per tenant
  @@index([tenantId])
  @@index([roomId])
  @@index([email])
  @@index([phone])
  @@index([tenantId, isGuest]) // For filtering guest/registered users
}

// Admin users who manage tenants (restaurant owners, managers, staff)
model AdminUser {
  id          String   @id @default(uuid())
  tenantId    String   // Foreign key to Tenant
  authUserId  String   @unique // Maps to Supabase auth.users.id
  email       String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  avatar      String?
  
  // Role-based access control within tenant
  role        AdminRole @default(ADMIN)
  
  // User status and permissions
  status      AdminStatus @default(ACTIVE)
  permissions String        @default("[]") // Array of permission strings
  
  // User preferences and settings
  preferences String        @default("{}")
  timezone    String      @default("UTC")
  language    String      @default("en")
  
  // Activity tracking
  lastLoginAt DateTime?
  isOnline    Boolean     @default(false)
  
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relations
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  auditLogs   AuditLog[]  // User activity logs
  
  @@index([tenantId])
  @@index([email])
  @@index([authUserId])
  @@index([tenantId, role])
  @@index([tenantId, status])
}

// Audit log for tracking important system events
model AuditLog {
  id         String   @id @default(uuid())
  tenantId   String   // Foreign key to Tenant
  userId     String?  // Foreign key to AdminUser (nullable for system events)
  action     String   // "user:login", "tenant:created", "order:updated"
  resource   String   // "user", "tenant", "order"
  resourceId String?  // ID of the affected resource
  ipAddress  String?  // Request IP address
  userAgent  String?  // User agent string
  metadata   String?    // Additional event-specific data
  createdAt  DateTime @default(now())
  
  // Relations
  tenant     Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user       AdminUser? @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@index([tenantId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@index([tenantId, createdAt])
}

// Rooms/Tables in the establishment
model Room {
  id          String   @id @default(uuid())
  tenantId    String   // Foreign key to Tenant
  roomNumber  String   // Room/Table number
  floor       String?  // Floor/Section information
  qrCode      String   // QR code for this room (unique within tenant)
  capacity    Int      @default(2) // Number of guests
  status      RoomStatus @default(AVAILABLE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  users       User[]   // Current guests
  orders      Order[]  // Orders from this room
  
  // Indexes for performance
  @@unique([tenantId, roomNumber]) // Room number unique per tenant
  @@unique([tenantId, qrCode]) // QR code unique per tenant
  @@index([tenantId])
  @@index([status])
  @@index([tenantId, status]) // Hot path: available rooms per tenant
}

// Allergen master list
model Allergen {
  id          String   @id @default(uuid())
  tenantId    String   // Foreign key to Tenant
  name        String   // e.g., "Peanuts", "Gluten", "Dairy"
  icon        String?  // Icon identifier
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  menuItems   MenuItemAllergen[]
  
  // Indexes
  @@unique([tenantId, name]) // Allergen name unique per tenant
  @@index([tenantId])
}

// Menu items available for ordering
model MenuItem {
  id            String   @id @default(uuid())
  tenantId      String   // Foreign key to Tenant
  name          String
  description   String?
  category      String   // e.g., "Appetizers", "Main Course", "Beverages"
  price         Decimal // Price with 2 decimal places
  imageUrl      String?
  isAvailable   Boolean  @default(true)
  isVegetarian  Boolean  @default(false)
  isVegan       Boolean  @default(false)
  preparationTime Int    @default(15) // Estimated prep time in minutes
  sortOrder     Int      @default(0) // For custom ordering in menu
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relations
  tenant        Tenant   @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  orderItems    OrderItem[]
  allergens     MenuItemAllergen[]
  
  // Indexes for performance
  @@index([tenantId])
  @@index([category])
  @@index([isAvailable])
  @@index([tenantId, category, sortOrder])
  @@index([tenantId, isAvailable]) // Hot path: available items per tenant
}

// Junction table for MenuItem-Allergen many-to-many relationship
model MenuItemAllergen {
  id          String   @id @default(uuid())
  menuItemId  String   // Foreign key to MenuItem
  allergenId  String   // Foreign key to Allergen
  severity    AllergenSeverity @default(CONTAINS) // CONTAINS, MAY_CONTAIN, TRACE
  createdAt   DateTime @default(now())
  
  // Relations
  menuItem    MenuItem @relation(fields: [menuItemId], references: [id], onDelete: Cascade)
  allergen    Allergen @relation(fields: [allergenId], references: [id], onDelete: Restrict)
  
  // Indexes
  @@unique([menuItemId, allergenId]) // Prevent duplicate allergen assignments
  @@index([menuItemId])
  @@index([allergenId])
}

// Orders placed by users
model Order {
  id            String      @id @default(uuid())
  tenantId      String      // Foreign key to Tenant
  userId        String      // Foreign key to User
  roomId        String      // Foreign key to Room
  orderNumber   String      // Human-readable order number
  status        OrderStatus @default(PENDING)
  totalAmount   Decimal
  notes         String?     // Special instructions
  assignedToId  String?     // Assigned staff member
  placedAt      DateTime    @default(now())
  confirmedAt   DateTime?   // When order was confirmed by kitchen
  completedAt   DateTime?   // When order was completed
  deliveredAt   DateTime?   // When order was delivered
  cancelledAt   DateTime?   // When order was cancelled
  
  // Relations
  tenant        Tenant      @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  user          User        @relation(fields: [userId], references: [id], onDelete: Restrict)
  room          Room        @relation(fields: [roomId], references: [id], onDelete: Restrict)
  assignedTo    Staff?      @relation(fields: [assignedToId], references: [id], onDelete: SetNull)
  items         OrderItem[]
  events        OrderEvent[]
  
  // Indexes for performance
  @@unique([tenantId, orderNumber]) // Order number unique per tenant
  @@index([tenantId])
  @@index([userId])
  @@index([roomId])
  @@index([status])
  @@index([assignedToId])
  @@index([placedAt])
  @@index([tenantId, status]) // Hot path: orders by status per tenant
  @@index([tenantId, placedAt]) // Hot path: recent orders
}

// Line items within an order
model OrderItem {
  id          String   @id @default(uuid())
  orderId     String   // Foreign key to Order
  menuItemId  String   // Foreign key to MenuItem
  quantity    Int      @default(1)
  unitPrice   Decimal // Price at time of order
  notes       String?  // Item-specific notes
  status      OrderItemStatus @default(PENDING)
  
  // Relations
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  menuItem    MenuItem @relation(fields: [menuItemId], references: [id], onDelete: Restrict)
  
  // Indexes for performance
  @@index([orderId])
  @@index([menuItemId])
  @@index([status])
  @@index([orderId, status]) // Hot path: item status within order
}

// Audit trail for order status changes
model OrderEvent {
  id          String      @id @default(uuid())
  tenantId    String      // Foreign key to Tenant
  orderId     String      // Foreign key to Order
  staffId     String?     // Who made the change (null for system)
  eventType   OrderEventType
  fromStatus  OrderStatus?
  toStatus    OrderStatus?
  metadata    String?       // Additional event data
  createdAt   DateTime    @default(now())
  
  // Relations
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  order       Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
  staff       Staff?      @relation(fields: [staffId], references: [id], onDelete: SetNull)
  
  // Indexes
  @@index([tenantId])
  @@index([orderId])
  @@index([staffId])
  @@index([createdAt])
  @@index([orderId, createdAt]) // Order history
}

// Staff members who manage orders
model Staff {
  id          String    @id @default(uuid())
  tenantId    String    // Foreign key to Tenant
  email       String
  name        String
  role        StaffRole
  pin         String    @default("0000") // 4-6 digit PIN with default
  isActive    Boolean   @default(true)
  lastLogin   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relations
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Restrict)
  orders      Order[]   // Orders assigned to this staff member
  orderEvents OrderEvent[] // Audit trail of actions
  
  // Indexes for performance
  @@unique([tenantId, email]) // Email unique per tenant
  @@unique([tenantId, pin])   // PIN unique per tenant
  @@index([tenantId])
  @@index([role])
  @@index([isActive])
  @@index([tenantId, isActive]) // Hot path: active staff per tenant
}

// Enums for status fields
enum RoomStatus {
  AVAILABLE
  OCCUPIED
  RESERVED
  MAINTENANCE
}

enum OrderStatus {
  PENDING      // Order placed, awaiting confirmation
  CONFIRMED    // Order confirmed by kitchen
  PREPARING    // Being prepared
  READY        // Ready for delivery
  DELIVERED    // Delivered to room/table
  COMPLETED    // Payment completed
  CANCELLED    // Order cancelled
}

enum OrderItemStatus {
  PENDING
  PREPARING
  READY
  SERVED
  CANCELLED
}

enum StaffRole {
  ADMIN        // Full access to tenant
  MANAGER      // Manage orders and staff
  CHEF         // Kitchen staff
  WAITER       // Service staff
  RECEPTIONIST // Front desk
}

enum AllergenSeverity {
  CONTAINS     // Definitely contains this allergen
  MAY_CONTAIN  // May contain traces
  TRACE        // Processed in facility that handles this
}

enum OrderEventType {
  STATUS_CHANGED
  ASSIGNED
  UNASSIGNED
  ITEM_ADDED
  ITEM_REMOVED
  ITEM_MODIFIED
  NOTE_ADDED
  CANCELLED
}

// New enums for tenant signup workflow
enum TenantStatus {
  ACTIVE      // Fully operational
  SUSPENDED   // Temporarily disabled
  INACTIVE    // Deactivated by admin
  PENDING     // Awaiting approval/setup
}

enum PlanType {
  TRIAL       // 30-day free trial
  BASIC       // Basic plan features
  PREMIUM     // Advanced features
  ENTERPRISE  // Full feature set
}

enum AdminRole {
  OWNER       // Tenant owner (full access)
  ADMIN       // Tenant administrator 
  MANAGER     // Restaurant manager
  STAFF       // Kitchen/serving staff
  READONLY    // View-only access
}

enum AdminStatus {
  ACTIVE      // Can access the system
  INACTIVE    // Temporarily disabled
  PENDING     // Awaiting email verification
  SUSPENDED   // Suspended by admin
}