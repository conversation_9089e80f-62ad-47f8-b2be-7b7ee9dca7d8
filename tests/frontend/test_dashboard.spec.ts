import { test, expect } from '@playwright/test';

test.describe('Admin Dashboard', () => {
  test('should display dashboard with correct layout', async ({ page }) => {
    await page.goto('/admin/dashboard');
    
    // Check page title
    await expect(page).toHaveTitle(/Admin Dashboard/);
    
    // Verify header elements
    await expect(page.locator('h1')).toContainText('Admin Dashboard');
    await expect(page.locator('text=Welcome back')).toBeVisible();
    
    // Verify stats grid
    await expect(page.locator('text=Active QR Codes')).toBeVisible();
    await expect(page.locator('text=Orders Today')).toBeVisible();
    await expect(page.locator('text=Active Staff')).toBeVisible();
    await expect(page.locator('text=Revenue Today')).toBeVisible();
    
    // Verify quick actions section
    await expect(page.locator('text=Quick Actions')).toBeVisible();
    await expect(page.locator('text=QR Code Management')).toBeVisible();
    await expect(page.locator('text=Order Management')).toBeVisible();
    await expect(page.locator('text=Staff Management')).toBeVisible();
    await expect(page.locator('text=Analytics')).toBeVisible();
  });

  test('should show welcome banner for new users', async ({ page }) => {
    await page.goto('/admin/dashboard?welcome=true');
    
    // Welcome banner should be visible
    await expect(page.locator('text=Welcome to BheemDine! 🎉')).toBeVisible();
    await expect(page.locator('text=Your account has been created successfully')).toBeVisible();
    
    // Quick setup steps should be visible
    await expect(page.locator('text=Account Created')).toBeVisible();
    await expect(page.locator('text=Add Menu Items')).toBeVisible();
    await expect(page.locator('text=Generate QR Codes')).toBeVisible();
  });

  test('should dismiss welcome banner', async ({ page }) => {
    await page.goto('/admin/dashboard?welcome=true');
    
    // Banner should be visible
    await expect(page.locator('text=Welcome to BheemDine! 🎉')).toBeVisible();
    
    // Click close button
    await page.click('button:has-text("✕")');
    
    // Banner should be hidden
    await expect(page.locator('text=Welcome to BheemDine! 🎉')).not.toBeVisible();
  });

  test('should navigate to different sections', async ({ page }) => {
    await page.goto('/admin/dashboard');
    
    // Test QR management navigation
    await page.click('text=QR Code Management');
    await expect(page).toHaveURL('/admin/qr');
    
    // Go back to dashboard
    await page.goto('/admin/dashboard');
    
    // Test order management navigation
    await page.click('text=Order Management');
    await expect(page).toHaveURL('/admin/orders');
  });

  test('should display recent activity sections', async ({ page }) => {
    await page.goto('/admin/dashboard');
    
    await expect(page.locator('text=Recent Orders')).toBeVisible();
    await expect(page.locator('text=QR Code Status')).toBeVisible();
    
    // Should show sample data or empty states
    const hasOrders = await page.locator('text=#1234').isVisible();
    const hasQRStatus = await page.locator('text=Table 1-10').isVisible();
    
    // At least one section should have content or show proper empty state
    expect(hasOrders || hasQRStatus).toBeTruthy();
  });

  test('should have working navigation buttons', async ({ page }) => {
    await page.goto('/admin/dashboard');
    
    // Test "Back to Home" button
    await page.click('text=Back to Home');
    await expect(page).toHaveURL('/');
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/admin/dashboard');
    
    // Should still display main elements
    await expect(page.locator('h1')).toContainText('Admin Dashboard');
    await expect(page.locator('text=Quick Actions')).toBeVisible();
  });

  test('should handle welcome banner setup actions', async ({ page }) => {
    await page.goto('/admin/dashboard?welcome=true');
    
    // Click "Add Menu Items" from welcome banner if visible
    const addMenuButton = page.locator('text=Add Menu Items');
    if (await addMenuButton.isVisible()) {
      await addMenuButton.click();
      await expect(page).toHaveURL('/admin/menu');
    }
  });
});