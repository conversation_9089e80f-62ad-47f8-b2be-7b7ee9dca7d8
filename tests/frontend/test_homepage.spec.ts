import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should display homepage with correct elements', async ({ page }) => {
    await page.goto('/');
    
    // Check page title
    await expect(page).toHaveTitle(/BHEEMDINE/);
    
    // Verify header elements
    await expect(page.locator('h1')).toContainText('BHEEMDINE');
    await expect(page.locator('text=Digital Restaurant Management')).toBeVisible();
    
    // Verify navigation buttons
    await expect(page.locator('text=Start Free Trial')).toBeVisible();
    await expect(page.locator('text=View Demo Menu')).toBeVisible();
    await expect(page.locator('text=Admin Dashboard')).toBeVisible();
    
    // Verify trust indicators
    await expect(page.locator('text=30-day free trial')).toBeVisible();
    await expect(page.locator('text=No credit card required')).toBeVisible();
    await expect(page.locator('text=Setup in 5 minutes')).toBeVisible();
  });

  test('should navigate to signup page from CTA buttons', async ({ page }) => {
    await page.goto('/');
    
    // Test main CTA button
    await page.click('button:has-text("Start Your Free Trial")');
    await expect(page).toHaveURL('/signup');
    
    // Go back and test header button
    await page.goto('/');
    await page.click('text=Start Free Trial');
    await expect(page).toHaveURL('/signup');
  });

  test('should navigate to demo menu', async ({ page }) => {
    await page.goto('/');
    
    await page.click('text=View Demo Menu');
    await expect(page).toHaveURL('/menu');
  });

  test('should navigate to admin dashboard', async ({ page }) => {
    await page.goto('/');
    
    await page.click('text=Admin Dashboard');
    await expect(page).toHaveURL('/admin/dashboard');
  });

  test('should display feature sections', async ({ page }) => {
    await page.goto('/');
    
    // Verify features grid
    await expect(page.locator('text=Everything You Need to Manage Your Restaurant')).toBeVisible();
    await expect(page.locator('text=QR Code Management')).toBeVisible();
    await expect(page.locator('text=Real-time Orders')).toBeVisible();
    await expect(page.locator('text=Staff Management')).toBeVisible();
    await expect(page.locator('text=Analytics Dashboard')).toBeVisible();
  });

  test('should display demo section', async ({ page }) => {
    await page.goto('/');
    
    await expect(page.locator('text=Try Our Demo')).toBeVisible();
    await expect(page.locator('text=Customer View')).toBeVisible();
    await expect(page.locator('text=Admin Dashboard')).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Should display main elements on mobile
    await expect(page.locator('h1')).toContainText('BHEEMDINE');
    await expect(page.locator('text=Digital Restaurant Management')).toBeVisible();
    await expect(page.locator('text=Start Your Free Trial')).toBeVisible();
  });

  test('should have working footer', async ({ page }) => {
    await page.goto('/');
    
    // Scroll to footer
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    await expect(page.locator('text=© 2024 BHEEMDINE. All rights reserved.')).toBeVisible();
  });
});