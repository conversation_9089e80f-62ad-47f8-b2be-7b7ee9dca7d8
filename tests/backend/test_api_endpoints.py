import pytest
from httpx import AsyncClient

class TestAPIEndpoints:
    """Test basic API endpoint availability and responses."""
    
    @pytest.mark.asyncio
    async def test_signup_endpoint_exists(self, client: AsyncClient):
        """Test that signup endpoint exists and handles requests."""
        # Test with empty data to check endpoint existence
        response = await client.post("/api/tenants/signup", json={})
        
        # Should return 400 for validation error, not 404
        assert response.status_code == 400
        
        data = response.json()
        assert "success" in data
        assert data["success"] is False

    @pytest.mark.asyncio
    async def test_availability_endpoint_exists(self, client: AsyncClient):
        """Test that availability check endpoint exists."""
        response = await client.get("/api/tenants/check-availability?email=<EMAIL>")
        
        # Should return 200 for successful check
        assert response.status_code == 200
        
        data = response.json()
        assert "email_available" in data

    @pytest.mark.asyncio
    async def test_availability_endpoint_validation(self, client: AsyncClient):
        """Test availability endpoint parameter validation."""
        # Test with no parameters
        response = await client.get("/api/tenants/check-availability")
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "error" in data

    @pytest.mark.asyncio
    async def test_unsupported_methods(self, client: AsyncClient):
        """Test that unsupported HTTP methods return 405."""
        # Test GET on signup endpoint
        response = await client.get("/api/tenants/signup")
        assert response.status_code == 405
        
        data = response.json()
        assert data["error_code"] == "METHOD_NOT_ALLOWED"
        
        # Test POST on availability endpoint
        response = await client.post("/api/tenants/check-availability", json={})
        assert response.status_code == 405
        
        data = response.json()
        assert data["error_code"] == "METHOD_NOT_ALLOWED"

    @pytest.mark.asyncio
    async def test_home_page_loads(self, client: AsyncClient):
        """Test that home page loads successfully."""
        response = await client.get("/")
        
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")

    @pytest.mark.asyncio
    async def test_signup_page_loads(self, client: AsyncClient):
        """Test that signup page loads successfully."""
        response = await client.get("/signup")
        
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")

    @pytest.mark.asyncio
    async def test_admin_dashboard_loads(self, client: AsyncClient):
        """Test that admin dashboard loads successfully."""
        response = await client.get("/admin/dashboard")
        
        assert response.status_code == 200
        assert "text/html" in response.headers.get("content-type", "")