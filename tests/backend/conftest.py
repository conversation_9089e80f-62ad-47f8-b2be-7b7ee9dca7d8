import pytest
import os
import asyncio
from httpx import As<PERSON><PERSON><PERSON>
from faker import Faker
from typing import Dict, Any, AsyncGenerator

# Set test environment
os.environ["DATABASE_URL"] = "file:./test.db"
os.environ["NODE_ENV"] = "test"

fake = Faker()

@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def client() -> AsyncGenerator[AsyncClient, None]:
    """HTTP client for API testing."""
    async with AsyncClient(base_url="http://localhost:3000") as ac:
        yield ac

@pytest.fixture
def valid_tenant_data() -> Dict[str, Any]:
    """Generate valid tenant signup data."""
    return {
        "tenant_name": fake.company(),
        "tenant_slug": fake.slug(),
        "email": fake.email(),
        "password": "TestPass123!",
        "first_name": fake.first_name(),
        "last_name": fake.last_name(),
        "phone": fake.phone_number(),
        "address": fake.street_address(),
        "city": fake.city(),
        "state": fake.state(),
        "zip_code": fake.zipcode()
    }

@pytest.fixture
def invalid_tenant_data() -> Dict[str, Any]:
    """Generate invalid tenant signup data for negative testing."""
    return {
        "tenant_name": "",  # Invalid: empty
        "tenant_slug": "a",  # Invalid: too short
        "email": "invalid-email",  # Invalid: not email format
        "password": "weak",  # Invalid: doesn't meet requirements
        "first_name": "",  # Invalid: empty
        "last_name": ""  # Invalid: empty
    }