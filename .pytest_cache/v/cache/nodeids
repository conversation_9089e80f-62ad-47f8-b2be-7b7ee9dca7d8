["tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_admin_dashboard_loads", "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_exists", "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_availability_endpoint_validation", "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_home_page_loads", "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_endpoint_exists", "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_signup_page_loads", "tests/backend/test_api_endpoints.py::TestAPIEndpoints::test_unsupported_methods", "tests/backend/test_live_api.py::TestLiveAPI::test_admin_dashboard_loads", "tests/backend/test_live_api.py::TestLiveAPI::test_availability_check_endpoint", "tests/backend/test_live_api.py::TestLiveAPI::test_availability_endpoint_validation", "tests/backend/test_live_api.py::TestLiveAPI::test_home_page_loads", "tests/backend/test_live_api.py::TestLiveAPI::test_signup_endpoint_validation", "tests/backend/test_live_api.py::TestLiveAPI::test_signup_page_loads", "tests/backend/test_live_api.py::TestLiveAPI::test_signup_password_validation", "tests/backend/test_live_api.py::TestLiveAPI::test_signup_with_valid_data_structure", "tests/backend/test_live_api.py::TestLiveAPI::test_unsupported_methods", "tests/backend/test_tenant_signup.py::TestTenantSignup::test_availability_check_endpoint", "tests/backend/test_tenant_signup.py::TestTenantSignup::test_email_format_validation", "tests/backend/test_tenant_signup.py::TestTenantSignup::test_invalid_tenant_signup", "tests/backend/test_tenant_signup.py::TestTenantSignup::test_missing_required_fields", "tests/backend/test_tenant_signup.py::TestTenantSignup::test_password_validation", "tests/backend/test_tenant_signup.py::TestTenantSignup::test_valid_tenant_signup_validation", "tests/test_order_service.py::TestOrderService::test_check_suspicious_patterns_high_quantity", "tests/test_order_service.py::TestOrderService::test_check_suspicious_patterns_sql_injection", "tests/test_order_service.py::TestOrderService::test_order_service_configuration", "tests/test_order_service.py::TestOrderService::test_process_payment_card_failure", "tests/test_order_service.py::TestOrderService::test_process_payment_card_success", "tests/test_order_service.py::TestOrderService::test_process_payment_cash", "tests/test_order_service.py::TestOrderService::test_repository_error_handling", "tests/test_order_service.py::TestOrderService::test_submit_order_amount_exceeded", "tests/test_order_service.py::TestOrderService::test_submit_order_success", "tests/test_order_service.py::TestOrderService::test_submit_order_suspicious_content", "tests/test_order_service.py::TestOrderService::test_submit_order_suspicious_pricing", "tests/test_order_service.py::TestOrderService::test_submit_order_too_many_items", "tests/test_order_service.py::TestOrderService::test_validate_business_hours_outside_hours", "tests/test_order_service.py::TestOrderService::test_validate_business_hours_within_hours", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_error_handling_edge_cases", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_format_phone_number", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_kitchen_notification_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_no_recipients", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_partial_failure", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_new_order_notification_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_order_status_update_failure", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_send_order_status_update_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_test_connection_success", "tests/test_whatsapp_notification.py::TestWhatsAppNotificationService::test_validate_phone_number"]